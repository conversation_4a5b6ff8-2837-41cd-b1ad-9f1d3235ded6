from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext

from .main import show_student_main_menu
from ..keyboards.homework import (
    get_courses_kb, get_subjects_kb, get_lessons_kb,
    get_confirm_kb, get_test_answers_kb, get_after_test_kb
)
from .test_logic import start_test_process, process_test_answer
from aiogram.fsm.state import State, StatesGroup
from database import HomeworkRepository, LessonRepository, SubjectRepository
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button

class HomeworkStates(StatesGroup):
    course = State()
    subject = State()
    lesson = State()
    homework = State()
    confirmation = State()
    test_in_progress = State()

router = Router()


@router.callback_query(F.data == "homework")
async def choose_course(callback: CallbackQuery, state: FSMContext):
    await callback.message.edit_text(
        "Выбери курс, по которому хочешь пройти домашнее задание 👇",
        reply_markup=get_courses_kb()
    )
    await state.set_state(HomeworkStates.course)

@router.callback_query(HomeworkStates.course, F.data.startswith("course_"))
async def choose_subject(callback: CallbackQuery, state: FSMContext):
    await state.update_data(course=callback.data)
    await callback.message.edit_text(
        "Теперь выбери предмет — это поможет выбрать нужные темы и задания 📚",
        reply_markup=get_subjects_kb()
    )
    await state.set_state(HomeworkStates.subject)

@router.callback_query(HomeworkStates.subject, F.data.startswith("sub_"))
async def choose_lesson(callback: CallbackQuery, state: FSMContext):
    """Выбор урока для предмета"""
    subject_code = callback.data.replace("sub_", "")

    # Получаем предмет по коду (нужно создать маппинг кодов к ID)
    subject_mapping = {
        "kz": "История Казахстана",
        "mathlit": "Математическая грамотность",
        "math": "Математика",
        "geo": "География",
        "bio": "Биология",
        "chem": "Химия",
        "inf": "Информатика",
        "world": "Всемирная история",
        "read": "Грамотность чтения"
    }

    subject_name = subject_mapping.get(subject_code)
    if not subject_name:
        await callback.answer("❌ Предмет не найден", show_alert=True)
        return

    # Получаем предмет из базы данных
    subjects = await SubjectRepository.get_all()
    subject = next((s for s in subjects if s.name == subject_name), None)

    if not subject:
        await callback.answer("❌ Предмет не найден в базе данных", show_alert=True)
        return

    await state.update_data(subject_id=subject.id, subject_name=subject.name)

    await callback.message.edit_text(
        f"📚 Предмет: {subject.name}\n\n"
        "Выбери урок, по которому хочешь пройти домашнее задание:",
        reply_markup=await get_lessons_kb(subject.id)
    )
    await state.set_state(HomeworkStates.lesson)

@router.callback_query(HomeworkStates.lesson, F.data.startswith("lesson_"))
async def choose_homework(callback: CallbackQuery, state: FSMContext):
    """Выбор домашнего задания для урока"""
    lesson_id = int(callback.data.replace("lesson_", ""))

    # Получаем урок из базы данных
    lesson = await LessonRepository.get_by_id(lesson_id)
    if not lesson:
        await callback.answer("❌ Урок не найден", show_alert=True)
        return

    # Получаем домашние задания для этого урока
    homeworks = await HomeworkRepository.get_by_lesson(lesson_id)

    if not homeworks:
        await callback.message.edit_text(
            f"📚 Урок: {lesson.name}\n\n"
            "❌ Для этого урока пока нет домашних заданий.",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                *get_main_menu_back_button()
            ])
        )
        return

    # Сохраняем данные урока в состоянии
    await state.update_data(lesson_id=lesson_id, lesson_name=lesson.name)

    # Формируем клавиатуру с реальными домашними заданиями
    buttons = []
    for homework in homeworks:
        buttons.append([InlineKeyboardButton(
            text=homework.name,
            callback_data=f"homework_{homework.id}"
        )])

    buttons.extend(get_main_menu_back_button())

    await callback.message.edit_text(
        f"📚 Урок: {lesson.name}\n\n"
        "Выберите домашнее задание:",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
    )
    await state.set_state(HomeworkStates.homework)

# Обработчик confirm_homework теперь находится в homework_quiz.py

@router.callback_query(HomeworkStates.confirmation, F.data == "start_test")
async def start_test(callback: CallbackQuery, state: FSMContext):
    await start_test_process(callback, state)
    await state.set_state(HomeworkStates.test_in_progress)

# Обработчик ответов на вопросы теста
@router.callback_query(HomeworkStates.test_in_progress, F.data.startswith("answer_"))
async def process_answer(callback: CallbackQuery, state: FSMContext):
    selected_answer = callback.data.replace("answer_", "")
    await process_test_answer(callback, state, selected_answer)

@router.callback_query(F.data == "retry_test")
async def retry_test(callback: CallbackQuery, state: FSMContext):
    await callback.message.edit_text(
        "Вот доступные домашние задания по этой теме👇",
        reply_markup=get_homeworks_kb()
    )
    await state.set_state(HomeworkStates.homework)
