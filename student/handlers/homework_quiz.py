from aiogram import Router, F
from aiogram.types import Poll, PollAnswer, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import KeyboardButton, ReplyKeyboardMarkup
from aiogram.filters import Command
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from datetime import datetime, timedelta
import logging

from database import (
    HomeworkRepository, QuestionRepository, AnswerOptionRepository,
    HomeworkResultRepository, QuestionResultRepository, StudentRepository
)
from common.navigation import log

router = Router()

class QuizStates(StatesGroup):
    confirming = State()
    testing = State()

@router.callback_query(F.data.startswith("homework_"))
async def confirm_test(callback: CallbackQuery, state: FSMContext):
    """Подтверждение начала домашнего задания"""
    await log("confirm_test", "student", state)

    homework_id = int(callback.data.split("_")[1])
    logging.info(f"Студент выбрал домашнее задание с ID: {homework_id}")

    # Получаем информацию о домашнем задании
    homework = await HomeworkRepository.get_by_id(homework_id)
    if not homework:
        logging.error(f"Домашнее задание с ID {homework_id} не найдено в базе данных")
        await callback.answer("❌ Домашнее задание не найдено", show_alert=True)
        return

    logging.info(f"Найдено домашнее задание: {homework.name} (ID: {homework.id})")

    # Получаем вопросы домашнего задания
    questions = await QuestionRepository.get_by_homework(homework_id)

    if not questions:
        await callback.answer("❌ В этом домашнем задании нет вопросов", show_alert=True)
        return

    # Сохраняем информацию о домашнем задании в состоянии (только ID для избежания проблем с сериализацией)
    question_ids = [q.id for q in questions]
    await state.update_data(
        homework_id=homework_id,
        question_ids=question_ids
    )

    logging.info(f"Сохранены данные в состояние: homework_id={homework_id}, question_ids={question_ids}")

    # Вычисляем среднее время на вопрос для отображения
    avg_time = sum(q.time_limit for q in questions) // len(questions)

    text = (
        f"🔎 Урок: {homework.lesson.name}\n"
        f"📚 Предмет: {homework.subject.name}\n"
        f"📝 Домашнее задание: {homework.name}\n"
        f"📋 Вопросов: {len(questions)}\n"
        f"⏱ Среднее время на вопрос: {avg_time} секунд\n"
        f"⚠️ Баллы будут начислены только за 100% правильных ответов.\n"
        "Ты готов?"
    )

    await callback.message.edit_text(text, reply_markup=InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(text="▶️ Начать тест", callback_data="start_quiz")],
            [InlineKeyboardButton(text="◀️ Назад", callback_data="back")]
        ]
    ))
    await state.set_state(QuizStates.confirming)

@router.callback_query(QuizStates.confirming, F.data == "start_quiz")
async def start_quiz(callback: CallbackQuery, state: FSMContext):
    """Начало прохождения домашнего задания"""
    await log("start_quiz", "student", state)

    data = await state.get_data()
    homework_id = data.get("homework_id")
    question_ids = data.get("question_ids", [])

    logging.info(f"Данные состояния при запуске теста: homework_id={homework_id}, question_ids={question_ids}")
    logging.info(f"Все данные состояния: {list(data.keys())}")

    if not homework_id or not question_ids:
        logging.error(f"Отсутствуют данные: homework_id={homework_id}, question_ids={question_ids}")
        await callback.answer("❌ Ошибка: данные домашнего задания не найдены", show_alert=True)
        return

    # Получаем данные заново из базы
    homework = await HomeworkRepository.get_by_id(homework_id)
    questions = await QuestionRepository.get_by_homework(homework_id)

    if not homework or not questions:
        logging.error(f"Не удалось получить данные из БД: homework={homework is not None}, questions={len(questions) if questions else 0}")
        await callback.answer("❌ Ошибка: данные домашнего задания не найдены", show_alert=True)
        return

    # Получаем ID студента
    student = await StudentRepository.get_by_telegram_id(callback.from_user.id)
    if not student:
        await callback.answer("❌ Студент не найден", show_alert=True)
        return

    # Проверяем, не проходил ли студент уже это домашнее задание
    existing_attempts = await HomeworkResultRepository.get_student_homework_attempts(student.id, homework_id)
    is_first_attempt = len(existing_attempts) == 0

    # Инициализируем состояние теста
    await state.update_data(
        student_id=student.id,
        score=0,
        q_index=0,
        total_questions=len(questions),
        is_first_attempt=is_first_attempt,
        question_results=[],
        start_time=datetime.now().isoformat(),
        questions=[{
            'id': q.id,
            'text': q.text,
            'photo_path': q.photo_path,
            'time_limit': q.time_limit,
            'microtopic_number': q.microtopic_number
        } for q in questions],
        homework={
            'id': homework.id,
            'name': homework.name,
            'subject_name': homework.subject.name,
            'lesson_name': homework.lesson.name
        }
    )

    await state.set_state(QuizStates.testing)
    await callback.answer()
    await send_next_question(callback.message.chat.id, state, callback.bot)


async def send_next_question(chat_id, state: FSMContext, bot):
    """Отправка следующего вопроса"""
    data = await state.get_data()
    index = data.get("q_index", 0)
    questions = data.get("questions", [])

    if index >= len(questions):
        # Завершаем тест
        await finish_test(chat_id, state, bot)
        return

    question = questions[index]

    # Получаем варианты ответов для вопроса
    answer_options = await AnswerOptionRepository.get_by_question(question.id)
    if not answer_options:
        await bot.send_message(chat_id, "❌ Ошибка: варианты ответов не найдены")
        return

    # Сортируем варианты по порядковому номеру
    answer_options.sort(key=lambda x: x.order_number)

    # Формируем список вариантов ответов и находим правильный
    options = []
    correct_option_id = None

    for i, option in enumerate(answer_options):
        options.append(option.text)
        if option.is_correct:
            correct_option_id = i

    if correct_option_id is None:
        await bot.send_message(chat_id, "❌ Ошибка: правильный ответ не найден")
        return

    # Сохраняем информацию о текущем вопросе
    await state.update_data(
        current_question=question,
        current_answer_options=answer_options,
        question_start_time=datetime.now()
    )

    # Используем индивидуальный таймер для каждого вопроса
    close_date = int((datetime.now() + timedelta(seconds=question.time_limit)).timestamp())

    # Формируем текст вопроса
    question_text = question.text
    if question.photo_path:
        # Если есть фото, сначала отправляем его
        await bot.send_photo(
            chat_id=chat_id,
            photo=question.photo_path,
            caption=f"Вопрос {index + 1}/{len(questions)}"
        )

    await bot.send_poll(
        chat_id=chat_id,
        question=f"Вопрос {index + 1}/{len(questions)}: {question_text}",
        options=options,
        type="quiz",
        correct_option_id=correct_option_id,
        is_anonymous=False,
        close_date=close_date
    )



@router.poll_answer()
async def handle_poll_answer(poll: PollAnswer, state: FSMContext):
    """Обработка ответа на вопрос"""
    await log("handle_poll_answer", "student", state)

    data = await state.get_data()
    index = data.get("q_index", 0)
    questions = data.get("questions", [])
    current_question = data.get("current_question")
    current_answer_options = data.get("current_answer_options", [])
    question_start_time = data.get("question_start_time")

    if index >= len(questions) or not current_question:
        return

    selected_option_index = poll.option_ids[0]
    selected_answer = current_answer_options[selected_option_index] if selected_option_index < len(current_answer_options) else None

    # Проверяем правильность ответа
    is_correct = selected_answer and selected_answer.is_correct

    # Вычисляем время, потраченное на ответ
    time_spent = None
    if question_start_time:
        time_spent = int((datetime.now() - question_start_time).total_seconds())

    # Сохраняем результат ответа на вопрос
    question_results = data.get("question_results", [])
    question_results.append({
        "question_id": current_question.id,
        "selected_answer_id": selected_answer.id if selected_answer else None,
        "is_correct": is_correct,
        "time_spent": time_spent,
        "microtopic_number": current_question.microtopic_number
    })

    # Обновляем счетчик правильных ответов
    score = data.get("score", 0)
    if is_correct:
        score += 1

    # Обновляем состояние
    await state.update_data(
        score=score,
        q_index=index + 1,
        question_results=question_results
    )

    # Отправляем следующий вопрос
    await send_next_question(poll.user.id, state, poll.bot)


async def finish_test(chat_id, state: FSMContext, bot):
    """Завершение теста и сохранение результатов"""
    await log("finish_test", "student", state)

    data = await state.get_data()
    homework_id = data.get("homework_id")
    student_id = data.get("student_id")
    score = data.get("score", 0)
    total_questions = data.get("total_questions", 0)
    is_first_attempt = data.get("is_first_attempt", True)
    question_results = data.get("question_results", [])
    homework = data.get("homework")

    if not homework_id or not student_id:
        await bot.send_message(chat_id, "❌ Ошибка при сохранении результатов")
        return

    try:
        # Вычисляем баллы (3 балла за вопрос, только если 100% правильных ответов и первая попытка)
        points_earned = 0
        if score == total_questions and is_first_attempt:
            points_earned = total_questions * 3

        # Создаем результат домашнего задания
        homework_result = await HomeworkResultRepository.create(
            student_id=student_id,
            homework_id=homework_id,
            total_questions=total_questions,
            correct_answers=score,
            points_earned=points_earned,
            is_first_attempt=is_first_attempt
        )

        # Сохраняем результаты по каждому вопросу
        for result_data in question_results:
            await QuestionResultRepository.create(
                homework_result_id=homework_result.id,
                question_id=result_data["question_id"],
                selected_answer_id=result_data["selected_answer_id"],
                is_correct=result_data["is_correct"],
                time_spent=result_data["time_spent"],
                microtopic_number=result_data["microtopic_number"]
            )

        # Формируем сообщение с результатами
        percentage = round((score / total_questions) * 100, 1) if total_questions > 0 else 0

        if score == total_questions:
            result_emoji = "🎉"
            result_text = "Отлично! Все ответы правильные!"
            if is_first_attempt:
                result_text += f"\n💰 Получено баллов: {points_earned}"
            else:
                result_text += "\n🔄 Это повторная попытка, баллы не начисляются"
        elif percentage >= 80:
            result_emoji = "👏"
            result_text = "Хорошо! Почти все правильно!"
        elif percentage >= 60:
            result_emoji = "👍"
            result_text = "Неплохо! Есть над чем поработать"
        else:
            result_emoji = "📚"
            result_text = "Стоит повторить материал"

        message = (
            f"{result_emoji} Тест завершен!\n\n"
            f"📝 {homework.name}\n"
            f"📚 {homework.subject.name}\n"
            f"📊 Результат: {score}/{total_questions} ({percentage}%)\n"
            f"{result_text}\n\n"
            f"💡 Ты можешь пройти тест повторно для закрепления материала"
        )

        # Кнопки для дальнейших действий
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Пройти еще раз", callback_data=f"homework_{homework_id}")],
            [InlineKeyboardButton(text="◀️ Назад", callback_data="back")],
            [InlineKeyboardButton(text="🏠 Главное меню", callback_data="back_to_main")]
        ])

        await bot.send_message(chat_id, message, reply_markup=keyboard)

    except Exception as e:
        logging.error(f"Ошибка при сохранении результатов теста: {e}")
        await bot.send_message(chat_id, "❌ Ошибка при сохранении результатов. Попробуйте еще раз.")

    # Очищаем состояние
    await state.clear()



